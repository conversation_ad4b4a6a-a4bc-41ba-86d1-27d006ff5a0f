#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="zhengshi"
PORT="8080"
LOG_FILE="./logs/restart.log"

# 创建日志目录
mkdir -p logs

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1" >> "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1" >> "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" >> "$LOG_FILE"
}

# 强制停止所有相关进程
force_stop() {
    log "🔍 查找并停止所有相关进程..."
    
    # 方法1: 通过端口杀死进程
    local pids=$(lsof -ti:$PORT 2>/dev/null)
    if [ ! -z "$pids" ]; then
        log "发现占用端口 $PORT 的进程: $pids"
        echo "$pids" | xargs kill -9 2>/dev/null
        log_success "已杀死占用端口 $PORT 的进程"
    fi
    
    # 方法2: 通过进程名杀死
    pkill -f "$PROJECT_NAME" 2>/dev/null && log_success "已杀死 $PROJECT_NAME 相关进程"
    pkill -f "go run" 2>/dev/null && log_success "已杀死 go run 进程"
    pkill -f "main.go" 2>/dev/null && log_success "已杀死 main.go 进程"
    
    # 方法3: 杀死所有Go进程（谨慎使用）
    # pkill -f "go" 2>/dev/null
    
    # 等待进程完全停止
    sleep 2
    
    # 再次检查端口是否被释放
    if lsof -ti:$PORT >/dev/null 2>&1; then
        log_warning "端口 $PORT 仍被占用，尝试强制杀死..."
        lsof -ti:$PORT | xargs kill -9 2>/dev/null
        sleep 1
    fi
    
    log_success "进程清理完成"
}

# 检查Go环境
check_go() {
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装或不在 PATH 中"
        exit 1
    fi
    log_success "Go 环境检查通过: $(go version)"
}

# 检查项目文件
check_project() {
    if [ ! -f "go.mod" ]; then
        log_error "未找到 go.mod 文件，请确保在项目根目录运行"
        exit 1
    fi
    
    if [ ! -f "cmd/server/main.go" ]; then
        log_error "未找到 cmd/server/main.go 文件"
        exit 1
    fi
    
    log_success "项目文件检查通过"
}

# 更新依赖
update_deps() {
    log "📦 更新Go依赖..."
    if go mod tidy; then
        log_success "依赖更新成功"
    else
        log_error "依赖更新失败"
        exit 1
    fi
}

# 编译检查
compile_check() {
    log "🔨 编译检查..."
    if go build -o /tmp/zhengshi_test ./cmd/server/main.go; then
        rm -f /tmp/zhengshi_test
        log_success "编译检查通过"
    else
        log_error "编译失败，请检查代码"
        exit 1
    fi
}

# 启动服务
start_service() {
    log "🚀 启动服务..."
    
    # 使用nohup在后台启动服务
    nohup go run ./cmd/server/main.go > ./logs/app.log 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > ./logs/app.pid
    
    log "服务已启动，PID: $pid"
    
    # 等待服务启动
    log "⏳ 等待服务启动..."
    for i in {1..10}; do
        if curl -s http://localhost:$PORT/health >/dev/null 2>&1; then
            log_success "服务启动成功！"
            log_success "健康检查: http://localhost:$PORT/health"
            log_success "API地址: http://localhost:$PORT/api/v1/image/analyze"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    log_error "服务启动失败或健康检查超时"
    return 1
}

# 显示服务状态
show_status() {
    echo
    log "📊 服务状态:"
    
    if [ -f "./logs/app.pid" ]; then
        local pid=$(cat ./logs/app.pid)
        if ps -p $pid > /dev/null 2>&1; then
            echo -e "${GREEN}  ✅ 服务运行中 (PID: $pid)${NC}"
        else
            echo -e "${RED}  ❌ 服务未运行 (PID文件存在但进程不存在)${NC}"
        fi
    else
        echo -e "${YELLOW}  ⚠️  未找到PID文件${NC}"
    fi
    
    if lsof -ti:$PORT >/dev/null 2>&1; then
        echo -e "${GREEN}  ✅ 端口 $PORT 被占用${NC}"
    else
        echo -e "${RED}  ❌ 端口 $PORT 未被占用${NC}"
    fi
    
    echo -e "${BLUE}  📝 日志文件: ./logs/app.log${NC}"
    echo -e "${BLUE}  📝 重启日志: $LOG_FILE${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "=================================="
    echo "    $PROJECT_NAME 服务重启脚本"
    echo "=================================="
    echo -e "${NC}"
    
    log "🔄 开始重启服务..."
    
    # 1. 强制停止所有进程
    force_stop
    
    # 2. 检查环境
    check_go
    check_project
    
    # 3. 更新依赖
    update_deps
    
    # 4. 编译检查
    compile_check
    
    # 5. 启动服务
    if start_service; then
        show_status
        log_success "🎉 服务重启完成！"
        
        echo
        echo -e "${GREEN}测试命令:${NC}"
        echo "curl http://localhost:$PORT/health"
        echo "curl -X POST http://localhost:$PORT/api/v1/image/analyze -H 'Content-Type: application/json' -d '{\"image_url\": \"https://httpbin.org/image/jpeg\"}'"
    else
        log_error "服务重启失败"
        echo
        echo -e "${YELLOW}查看日志:${NC}"
        echo "tail -f ./logs/app.log"
        echo "tail -f $LOG_FILE"
        exit 1
    fi
}

# 运行主函数
main "$@"
