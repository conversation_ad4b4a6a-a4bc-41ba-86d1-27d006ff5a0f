#!/bin/bash

echo "🛑 停止服务..."

# 多种方式停止服务
pkill -f "zhengshi" 2>/dev/null && echo "✅ 已停止 zhengshi 进程"
pkill -f "go run" 2>/dev/null && echo "✅ 已停止 go run 进程"
pkill -f "main.go" 2>/dev/null && echo "✅ 已停止 main.go 进程"

# 通过端口杀死进程
if lsof -ti:8080 >/dev/null 2>&1; then
    lsof -ti:8080 | xargs kill -9 2>/dev/null
    echo "✅ 已释放端口 8080"
fi

# 删除PID文件
rm -f ./logs/app.pid

sleep 1

# 检查是否完全停止
if lsof -ti:8080 >/dev/null 2>&1; then
    echo "⚠️  端口 8080 仍被占用"
else
    echo "✅ 服务已完全停止"
fi
