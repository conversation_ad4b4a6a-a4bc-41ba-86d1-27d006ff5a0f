[2025-06-13 21:17:49] 🔄 开始重启服务...
[2025-06-13 21:17:49] 🔍 查找并停止所有相关进程...
[2025-06-13 21:17:49] 发现占用端口 8080 的进程: 17821
[2025-06-13 21:17:49] SUCCESS: 已杀死占用端口 8080 的进程
[2025-06-13 21:17:51] SUCCESS: 进程清理完成
[2025-06-13 21:17:51] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-13 21:17:51] SUCCESS: 项目文件检查通过
[2025-06-13 21:17:51] 📦 更新Go依赖...
[2025-06-13 21:17:51] SUCCESS: 依赖更新成功
[2025-06-13 21:17:51] 🔨 编译检查...
[2025-06-13 21:17:53] SUCCESS: 编译检查通过
[2025-06-13 21:17:53] 🚀 启动服务...
[2025-06-13 21:17:53] 服务已启动，PID: 18007
[2025-06-13 21:17:53] ⏳ 等待服务启动...
[2025-06-13 21:17:54] SUCCESS: 服务启动成功！
[2025-06-13 21:17:54] SUCCESS: 健康检查: http://localhost:8080/health
[2025-06-13 21:17:54] SUCCESS: API地址: http://localhost:8080/api/v1/image/analyze
[2025-06-13 21:17:54] 📊 服务状态:
[2025-06-13 21:17:54] SUCCESS: 🎉 服务重启完成！
[2025-06-13 21:20:53] 🔄 开始重启服务...
[2025-06-13 21:20:53] 🔍 查找并停止所有相关进程...
[2025-06-13 21:20:53] 发现占用端口 8080 的进程: 17876
18030
[2025-06-13 21:20:53] SUCCESS: 已杀死占用端口 8080 的进程
[2025-06-13 21:20:55] SUCCESS: 进程清理完成
[2025-06-13 21:20:55] SUCCESS: Go 环境检查通过: go version go1.24.0 darwin/arm64
[2025-06-13 21:20:55] SUCCESS: 项目文件检查通过
[2025-06-13 21:20:55] 📦 更新Go依赖...
[2025-06-13 21:20:55] SUCCESS: 依赖更新成功
[2025-06-13 21:20:55] 🔨 编译检查...
[2025-06-13 21:20:56] SUCCESS: 编译检查通过
[2025-06-13 21:20:56] 🚀 启动服务...
[2025-06-13 21:20:56] 服务已启动，PID: 18437
[2025-06-13 21:20:56] ⏳ 等待服务启动...
[2025-06-13 21:20:58] SUCCESS: 服务启动成功！
[2025-06-13 21:20:58] SUCCESS: 健康检查: http://localhost:8080/health
[2025-06-13 21:20:58] SUCCESS: API地址: http://localhost:8080/api/v1/image/analyze
[2025-06-13 21:20:58] 📊 服务状态:
[2025-06-13 21:20:58] SUCCESS: 🎉 服务重启完成！
