package handler

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"zhengshi/internal/model"
	"zhengshi/internal/service"
	"zhengshi/internal/utils"

	"github.com/gin-gonic/gin"
)

// ImageHandler 图片处理器
type ImageHandler struct {
	imageService service.ImageService
	logger       utils.Logger
}

// NewImageHandler 创建图片处理器
func NewImageHandler(imageService service.ImageService, logger utils.Logger) *ImageHandler {
	return &ImageHandler{
		imageService: imageService,
		logger:       logger,
	}
}

// AnalyzeImage 分析图片 - 第一步实现：接收请求并返回200状态
// @Summary 分析图片
// @Description 接收图片URL，创建处理上下文并返回接收确认
// @Tags 图片分析
// @Accept json
// @Produce json
// @Param request body model.ImageAnalysisRequest true "图片分析请求"
// @Success 200 {object} model.APIResponse{data=model.ProcessContext}
// @Failure 400 {object} model.APIResponse
// @Failure 500 {object} model.APIResponse
// @Router /api/v1/image/analyze [post]
func (h *ImageHandler) AnalyzeImage(c *gin.Context) {
	var req model.ImageAnalysisRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters", "error", err.Error())
		h.respondWithError(c, utils.NewAPIError(utils.CodeInvalidParams, err.Error()))
		return
	}

	// 生成追踪ID
	traceID := h.generateTraceID()
	c.Set("trace_id", traceID)

	h.logger.Info("Image analysis request received",
		"trace_id", traceID,
		"image_url", req.ImageURL,
	)

	// 验证图片有效性
	if !h.isValidImage(req.ImageURL) {
		h.logger.Warn("Invalid image URL",
			"trace_id", traceID,
			"image_url", req.ImageURL,
		)
		h.respondWithError(c, utils.NewAPIError(utils.CodeImageURLInvalid))
		return
	}

	h.logger.Info("Image validation passed",
		"trace_id", traceID,
		"image_url", req.ImageURL,
	)

	// 创建贯穿整个处理流程的上下文结构体，存储图片URL
	processCtx := model.NewProcessContext(traceID, req.ImageURL)

	h.logger.Info("Process context created with image URL",
		"trace_id", traceID,
		"image_url", req.ImageURL,
		"status", processCtx.Status,
		"current_step", processCtx.CurrentStep,
	)

	// 成功接收请求，返回200状态码
	response := map[string]interface{}{
		"message": "图片分析请求已成功接收",
		"context": processCtx,
	}

	h.respondWithSuccess(c, response, traceID)
}

// GetAnalysisResult 获取分析结果
// @Summary 获取分析结果
// @Description 根据ID获取图片分析结果
// @Tags 图片分析
// @Accept json
// @Produce json
// @Param id path int true "分析结果ID"
// @Success 200 {object} model.APIResponse{data=model.ImageAnalysisResponse}
// @Failure 400 {object} model.APIResponse
// @Failure 404 {object} model.APIResponse
// @Failure 500 {object} model.APIResponse
// @Router /api/v1/image/analysis/{id} [get]
func (h *ImageHandler) GetAnalysisResult(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.Warn("Invalid analysis ID", "id", idStr, "error", err.Error())
		h.respondWithError(c, utils.NewAPIError(utils.CodeInvalidParams, "无效的分析ID"))
		return
	}

	traceID := h.generateTraceID()
	c.Set("trace_id", traceID)

	h.logger.Info("Getting analysis result", 
		"trace_id", traceID,
		"analysis_id", id,
	)

	result, err := h.imageService.GetAnalysisResult(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to get analysis result", err,
			"trace_id", traceID,
			"analysis_id", id,
		)
		
		if apiErr, ok := utils.IsAPIError(err); ok {
			h.respondWithError(c, apiErr)
		} else {
			h.respondWithError(c, utils.NewAPIError(utils.CodeInternalError, err.Error()))
		}
		return
	}

	h.logger.Info("Analysis result retrieved successfully",
		"trace_id", traceID,
		"analysis_id", id,
	)

	h.respondWithSuccess(c, result, traceID)
}

// respondWithSuccess 成功响应
func (h *ImageHandler) respondWithSuccess(c *gin.Context, data interface{}, traceID string) {
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    utils.CodeSuccess,
		Message: utils.GetErrorMessage(utils.CodeSuccess),
		Data:    data,
		TraceID: traceID,
	})
}

// respondWithError 错误响应
func (h *ImageHandler) respondWithError(c *gin.Context, err *utils.APIError) {
	traceID, _ := c.Get("trace_id")
	traceIDStr, _ := traceID.(string)
	
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    err.Code,
		Message: err.Message,
		TraceID: traceIDStr,
	})
}

// generateTraceID 生成追踪ID
func (h *ImageHandler) generateTraceID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 36)
}

// isValidImage 验证图片是否有效
func (h *ImageHandler) isValidImage(imageURL string) bool {
	// 1. 基础格式检查
	if !h.isValidURL(imageURL) {
		h.logger.Warn("Invalid URL format", "image_url", imageURL)
		return false
	}

	// 2. HTTP可访问性检查
	if !h.isImageAccessible(imageURL) {
		return false
	}

	return true
}

// isValidURL 检查URL格式是否有效
func (h *ImageHandler) isValidURL(imageURL string) bool {
	if strings.TrimSpace(imageURL) == "" {
		return false
	}

	// 检查是否以http或https开头
	if !strings.HasPrefix(strings.ToLower(imageURL), "http://") &&
	   !strings.HasPrefix(strings.ToLower(imageURL), "https://") {
		return false
	}

	return true
}

// isImageAccessible 检查图片是否可访问且为有效图片
func (h *ImageHandler) isImageAccessible(imageURL string) bool {
	// 创建HTTP客户端，设置合理的超时时间
	client := &http.Client{
		Timeout: 5 * time.Second, // 减少超时时间，提升响应速度
		Transport: &http.Transport{
			MaxIdleConns:        10,
			IdleConnTimeout:     30 * time.Second,
			DisableCompression:  true,
		},
	}

	// 创建HEAD请求（比GET更高效）
	req, err := http.NewRequest("HEAD", imageURL, nil)
	if err != nil {
		h.logger.Warn("Failed to create HTTP request",
			"error", err.Error(),
			"image_url", imageURL)
		return false
	}

	// 设置User-Agent，避免某些服务器拒绝请求
	req.Header.Set("User-Agent", "Mozilla/5.0 (compatible; ImageValidator/1.0)")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		h.logger.Warn("Failed to access image URL",
			"error", err.Error(),
			"image_url", imageURL)
		return false
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		h.logger.Warn("Image URL returned non-success status",
			"status_code", resp.StatusCode,
			"image_url", imageURL)
		return false
	}

	// 检查Content-Type
	contentType := resp.Header.Get("Content-Type")
	if !h.isImageContentType(contentType) {
		h.logger.Warn("Invalid content type for image",
			"content_type", contentType,
			"image_url", imageURL)
		return false
	}

	// 检查Content-Length（可选，避免过大文件）
	if contentLength := resp.Header.Get("Content-Length"); contentLength != "" {
		if length, err := strconv.ParseInt(contentLength, 10, 64); err == nil {
			const maxImageSize = 50 * 1024 * 1024 // 50MB限制
			if length > maxImageSize {
				h.logger.Warn("Image file too large",
					"content_length", length,
					"max_size", maxImageSize,
					"image_url", imageURL)
				return false
			}
		}
	}

	return true
}

// isImageContentType 检查Content-Type是否为图片类型
func (h *ImageHandler) isImageContentType(contentType string) bool {
	imageTypes := []string{
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/gif",
		"image/webp",
		"image/bmp",
		"image/tiff",
		"image/svg+xml",
	}

	for _, imageType := range imageTypes {
		if strings.Contains(strings.ToLower(contentType), imageType) {
			return true
		}
	}

	return false
}
