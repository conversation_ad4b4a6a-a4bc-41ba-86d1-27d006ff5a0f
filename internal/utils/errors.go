package utils

import "fmt"

// 错误码定义
const (
	// 成功
	CodeSuccess = 0

	// 通用错误码 1000-1999
	CodeInternalError   = 1000
	CodeInvalidParams   = 1001
	CodeUnauthorized    = 1002
	CodeForbidden       = 1003
	CodeNotFound        = 1004
	CodeMethodNotAllowed = 1005
	CodeTooManyRequests = 1006

	// 业务错误码 2000-2999
	CodeImageURLInvalid = 2000
)

// 错误信息映射
var errorMessages = map[int]string{
	CodeSuccess: "成功",

	// 通用错误
	CodeInternalError:    "内部服务器错误",
	CodeInvalidParams:    "参数错误",
	CodeUnauthorized:     "未授权",
	CodeForbidden:        "禁止访问",
	CodeNotFound:         "资源不存在",
	CodeMethodNotAllowed: "方法不被允许",
	CodeTooManyRequests:  "请求过于频繁",

	// 业务错误
	CodeImageURLInvalid: "图片不存在,可能是上传失败,请联系管理员处理！",
}

// APIError 自定义错误结构
type APIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error 实现error接口
func (e *APIError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("Code: %d, Message: %s, Details: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("Code: %d, Message: %s", e.Code, e.Message)
}

// NewAPIError 创建新的API错误
func NewAPIError(code int, details ...string) *APIError {
	message, exists := errorMessages[code]
	if !exists {
		message = "未知错误"
	}

	err := &APIError{
		Code:    code,
		Message: message,
	}

	if len(details) > 0 {
		err.Details = details[0]
	}

	return err
}

// GetErrorMessage 获取错误信息
func GetErrorMessage(code int) string {
	if message, exists := errorMessages[code]; exists {
		return message
	}
	return "未知错误"
}

// IsAPIError 判断是否为API错误
func IsAPIError(err error) (*APIError, bool) {
	if apiErr, ok := err.(*APIError); ok {
		return apiErr, true
	}
	return nil, false
}
