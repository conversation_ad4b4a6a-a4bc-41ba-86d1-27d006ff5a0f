package router

import (
	"zhengshi/internal/config"
	"zhengshi/internal/handler"
	"zhengshi/internal/middleware"
	"zhengshi/internal/repository"
	"zhengshi/internal/service"
	"zhengshi/internal/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// NewRouter 创建路由
func NewRouter(
	cfg *config.Config,
	db *gorm.DB,
	rdb utils.RedisClient,
	logger utils.Logger,
) *gin.Engine {
	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(middleware.Recovery(logger))
	r.Use(middleware.RequestLogger(logger))
	r.Use(middleware.CORS())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "Service is running",
		})
	})

	// 初始化仓储层
	imageRepo := repository.NewImageRepository(db)

	// 初始化服务层
	qwenService := service.NewQwenService(cfg.AI.Qwen, logger)
	deepSeekService := service.NewDeepSeekService(cfg.AI.DeepSeek, logger)
	imageService := service.NewImageService(imageRepo, qwenService, deepSeekService, rdb, logger)

	// 初始化处理器
	imageHandler := handler.NewImageHandler(imageService, logger)

	// API路由组
	v1 := r.Group("/api/v1")
	{
		// 图片分析相关路由
		imageGroup := v1.Group("/image")
		{
			imageGroup.POST("/analyze", imageHandler.AnalyzeImage)
			imageGroup.GET("/analysis/:id", imageHandler.GetAnalysisResult)
		}
	}

	return r
}
