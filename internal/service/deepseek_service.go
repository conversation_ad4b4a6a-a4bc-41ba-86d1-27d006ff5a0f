package service

import (
	"context"
	"zhengshi/internal/config"
	"zhengshi/internal/model"
	"zhengshi/internal/utils"
	"zhengshi/pkg/client"
)

// DeepSeekService DeepSeek服务接口
type DeepSeekService interface {
	ProcessData(ctx context.Context, data *model.ParsedQwenData, traceID string) (string, error)
}

// deepSeekService DeepSeek服务实现
type deepSeekService struct {
	client client.DeepSeekClient
	logger utils.Logger
}

// NewDeepSeekService 创建DeepSeek服务
func NewDeepSeekService(cfg config.DeepSeekConfig, logger utils.Logger) DeepSeekService {
	deepSeekClient := client.NewDeepSeekClient(cfg, logger)
	return &deepSeekService{
		client: deepSeekClient,
		logger: logger,
	}
}

// ProcessData 处理数据
func (s *deepSeekService) ProcessData(ctx context.Context, data *model.ParsedQwenData, traceID string) (string, error) {
	s.logger.Info("Starting DeepSeek data processing", 
		"trace_id", traceID,
		"data_description", data.Description,
	)

	result, err := s.client.ProcessData(ctx, data)
	if err != nil {
		s.logger.Error("DeepSeek data processing failed", err,
			"trace_id", traceID,
			"data_description", data.Description,
		)
		return "", utils.NewAPIError(utils.CodeDeepSeekAPIError, err.Error())
	}

	s.logger.Info("DeepSeek data processing completed",
		"trace_id", traceID,
		"data_description", data.Description,
	)

	return result, nil
}
