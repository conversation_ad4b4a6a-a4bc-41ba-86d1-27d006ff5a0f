package service

import (
	"context"

	"zhengshi/internal/model"
	"zhengshi/internal/repository"
	"zhengshi/internal/utils"
)

// ImageService 图片服务接口
type ImageService interface {
	// 当前只需要这个方法用于获取分析结果
	GetAnalysisResult(ctx context.Context, id uint) (*model.ImageAnalysisResponse, error)
}

// imageService 图片服务实现
type imageService struct {
	imageRepo repository.ImageRepository
	logger    utils.Logger
}

// NewImageService 创建图片服务
func NewImageService(
	imageRepo repository.ImageRepository,
	qwenService QwenService,
	deepSeekService DeepSeekService,
	cache utils.RedisClient,
	logger utils.Logger,
) ImageService {
	return &imageService{
		imageRepo: imageRepo,
		logger:    logger,
	}
}



// GetAnalysisResult 获取分析结果
func (s *imageService) GetAnalysisResult(ctx context.Context, id uint) (*model.ImageAnalysisResponse, error) {
	analysis, err := s.imageRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get analysis result", err, "analysis_id", id)
		return nil, utils.NewAPIError(utils.CodeNotFound, "分析结果不存在")
	}

	return &model.ImageAnalysisResponse{
		ID:        analysis.ID,
		ImageURL:  analysis.ImageURL,
		CreatedAt: analysis.CreatedAt,
	}, nil
}


