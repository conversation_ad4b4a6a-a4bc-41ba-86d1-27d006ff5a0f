package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"zhengshi/internal/model"
	"zhengshi/internal/repository"
	"zhengshi/internal/utils"
)

// ImageService 图片服务接口
type ImageService interface {
	AnalyzeImage(ctx context.Context, imageURL, traceID string) (*model.ImageAnalysisResponse, error)
	GetAnalysisResult(ctx context.Context, id uint) (*model.ImageAnalysisResponse, error)
	ProcessWithQwen(ctx context.Context, processCtx *model.ProcessContext) (*model.ProcessContext, error)
}

// imageService 图片服务实现
type imageService struct {
	imageRepo     repository.ImageRepository
	qwenService   QwenService
	deepSeekService DeepSeekService
	cache         utils.RedisClient
	logger        utils.Logger
}

// NewImageService 创建图片服务
func NewImageService(
	imageRepo repository.ImageRepository,
	qwenService QwenService,
	deepSeekService DeepSeekService,
	cache utils.RedisClient,
	logger utils.Logger,
) ImageService {
	return &imageService{
		imageRepo:       imageRepo,
		qwenService:     qwenService,
		deepSeekService: deepSeekService,
		cache:           cache,
		logger:          logger,
	}
}

// AnalyzeImage 分析图片
func (s *imageService) AnalyzeImage(ctx context.Context, imageURL, traceID string) (*model.ImageAnalysisResponse, error) {
	startTime := time.Now()

	// 创建分析记录
	analysis := &model.ImageAnalysis{
		ImageURL:    imageURL,
		Status:      "processing",
		ProcessTime: 0,
	}

	// 保存初始记录
	if err := s.imageRepo.Create(ctx, analysis); err != nil {
		s.logger.Error("Failed to create analysis record", err, "trace_id", traceID)
		return nil, utils.NewAPIError(utils.CodeDatabaseError, "创建分析记录失败")
	}

	s.logger.Info("Analysis record created", 
		"trace_id", traceID,
		"analysis_id", analysis.ID,
		"image_url", imageURL,
	)

	// 步骤1: 调用Qwen模型分析图片
	qwenResult, err := s.qwenService.AnalyzeImage(ctx, imageURL, traceID)
	if err != nil {
		s.updateAnalysisStatus(ctx, analysis.ID, "failed", err.Error())
		return nil, err
	}

	// 步骤2: 格式化解析Qwen响应数据
	parsedData, err := s.parseQwenResponse(qwenResult)
	if err != nil {
		s.logger.Error("Failed to parse Qwen response", err, "trace_id", traceID)
		s.updateAnalysisStatus(ctx, analysis.ID, "failed", "解析Qwen响应失败")
		return nil, utils.NewAPIError(utils.CodeImageProcessFailed, "解析Qwen响应失败")
	}

	// 步骤3: 生成缓存键名称
	cacheKey := s.generateCacheKey(parsedData)
	
	// 步骤4: 检查缓存
	cachedResult, err := s.getCachedResult(ctx, cacheKey)
	if err == nil && cachedResult != "" {
		s.logger.Info("Using cached DeepSeek result", "trace_id", traceID, "cache_key", cacheKey)
		
		// 使用缓存结果
		mergedResult := s.mergeResults(parsedData, cachedResult)
		processTime := time.Since(startTime).Milliseconds()
		
		// 更新分析记录
		analysis.QwenResult = qwenResult
		analysis.QwenParsed = s.structToJSON(parsedData)
		analysis.DeepSeekResult = cachedResult
		analysis.MergedResult = mergedResult
		analysis.CacheKey = cacheKey
		analysis.ProcessTime = processTime
		analysis.Status = "completed"
		
		if err := s.imageRepo.Update(ctx, analysis); err != nil {
			s.logger.Error("Failed to update analysis record", err, "trace_id", traceID)
		}
		
		return s.buildResponse(analysis), nil
	}

	// 步骤5: 调用DeepSeek模型
	deepSeekResult, err := s.deepSeekService.ProcessData(ctx, parsedData, traceID)
	if err != nil {
		s.updateAnalysisStatus(ctx, analysis.ID, "failed", err.Error())
		return nil, err
	}

	// 步骤6: 缓存DeepSeek结果
	if err := s.setCachedResult(ctx, cacheKey, deepSeekResult); err != nil {
		s.logger.Warn("Failed to cache DeepSeek result", "error", err.Error(), "trace_id", traceID)
	}

	// 步骤7: 合并结果
	mergedResult := s.mergeResults(parsedData, deepSeekResult)
	processTime := time.Since(startTime).Milliseconds()

	// 更新分析记录
	analysis.QwenResult = qwenResult
	analysis.QwenParsed = s.structToJSON(parsedData)
	analysis.DeepSeekResult = deepSeekResult
	analysis.MergedResult = mergedResult
	analysis.CacheKey = cacheKey
	analysis.ProcessTime = processTime
	analysis.Status = "completed"

	if err := s.imageRepo.Update(ctx, analysis); err != nil {
		s.logger.Error("Failed to update analysis record", err, "trace_id", traceID)
		return nil, utils.NewAPIError(utils.CodeDatabaseError, "更新分析记录失败")
	}

	s.logger.Info("Image analysis completed successfully",
		"trace_id", traceID,
		"analysis_id", analysis.ID,
		"process_time", processTime,
	)

	return s.buildResponse(analysis), nil
}

// ProcessWithQwen 处理ProcessContext并提交给Qwen
func (s *imageService) ProcessWithQwen(ctx context.Context, processCtx *model.ProcessContext) (*model.ProcessContext, error) {
	s.logger.Info("Starting Qwen processing with ProcessContext",
		"trace_id", processCtx.TraceID,
		"image_url", processCtx.ImageURL,
		"status", processCtx.Status,
	)

	// 记录Qwen处理开始时间
	qwenStartTime := time.Now()

	// 调用Qwen服务分析图片
	qwenResult, err := s.qwenService.AnalyzeImage(ctx, processCtx.ImageURL, processCtx.TraceID)
	if err != nil {
		s.logger.Error("Qwen service failed", err,
			"trace_id", processCtx.TraceID,
			"image_url", processCtx.ImageURL,
		)

		// 更新ProcessContext错误状态
		processCtx.SetError(err, "Qwen模型调用失败")
		return processCtx, utils.NewAPIError(utils.CodeQwenAPIError, err.Error())
	}

	// 计算Qwen处理时长
	processCtx.QwenDuration = time.Since(qwenStartTime).Milliseconds()

	// 解析Qwen响应
	parsedData, err := s.parseQwenResponse(qwenResult)
	if err != nil {
		s.logger.Error("Failed to parse Qwen response", err,
			"trace_id", processCtx.TraceID,
		)

		processCtx.SetError(err, "解析Qwen响应失败")
		return processCtx, utils.NewAPIError(utils.CodeImageProcessFailed, "解析Qwen响应失败")
	}

	// 更新ProcessContext状态和结果
	processCtx.UpdateStatus("qwen_completed", "Qwen模型处理完成")
	processCtx.QwenRawResult = qwenResult
	processCtx.QwenParsedData = parsedData
	processCtx.CacheKey = s.generateCacheKey(parsedData)

	s.logger.Info("Qwen processing completed successfully",
		"trace_id", processCtx.TraceID,
		"image_url", processCtx.ImageURL,
		"qwen_duration", processCtx.QwenDuration,
		"cache_key", processCtx.CacheKey,
	)

	return processCtx, nil
}

// GetAnalysisResult 获取分析结果
func (s *imageService) GetAnalysisResult(ctx context.Context, id uint) (*model.ImageAnalysisResponse, error) {
	analysis, err := s.imageRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get analysis result", err, "analysis_id", id)
		return nil, utils.NewAPIError(utils.CodeNotFound, "分析结果不存在")
	}

	return s.buildResponse(analysis), nil
}

// parseQwenResponse 解析Qwen响应
func (s *imageService) parseQwenResponse(qwenResult string) (*model.ParsedQwenData, error) {
	// TODO: 实现具体的解析逻辑，根据Qwen返回的格式进行解析
	// 这里提供一个示例实现
	var parsedData model.ParsedQwenData
	
	// 简单的JSON解析示例，实际需要根据Qwen的响应格式调整
	if err := json.Unmarshal([]byte(qwenResult), &parsedData); err != nil {
		// 如果直接JSON解析失败，可以实现自定义解析逻辑
		parsedData = model.ParsedQwenData{
			Description: qwenResult,
			Keywords:    []string{"image", "analysis"},
			Metadata:    make(map[string]interface{}),
		}
	}
	
	return &parsedData, nil
}

// generateCacheKey 生成缓存键
func (s *imageService) generateCacheKey(data *model.ParsedQwenData) string {
	// 基于解析数据的关键字段生成缓存键
	keyData := fmt.Sprintf("%s_%v_%s", 
		data.Description, 
		data.Keywords, 
		data.Scene,
	)
	return utils.GenerateCacheKey("deepseek", keyData)
}

// getCachedResult 获取缓存结果
func (s *imageService) getCachedResult(ctx context.Context, cacheKey string) (string, error) {
	return s.cache.Get(ctx, cacheKey)
}

// setCachedResult 设置缓存结果
func (s *imageService) setCachedResult(ctx context.Context, cacheKey, result string) error {
	// 设置24小时过期时间
	return s.cache.Set(ctx, cacheKey, result, 24*time.Hour)
}

// mergeResults 合并结果
func (s *imageService) mergeResults(qwenData *model.ParsedQwenData, deepSeekResult string) string {
	// TODO: 实现具体的合并逻辑
	mergedData := map[string]interface{}{
		"qwen_analysis": qwenData,
		"deepseek_result": deepSeekResult,
		"merged_at": time.Now(),
	}
	
	result, _ := json.Marshal(mergedData)
	return string(result)
}

// buildResponse 构建响应
func (s *imageService) buildResponse(analysis *model.ImageAnalysis) *model.ImageAnalysisResponse {
	return &model.ImageAnalysisResponse{
		ID:             analysis.ID,
		ImageURL:       analysis.ImageURL,
		QwenResult:     analysis.QwenResult,
		DeepSeekResult: analysis.DeepSeekResult,
		MergedResult:   analysis.MergedResult,
		ProcessTime:    analysis.ProcessTime,
		CreatedAt:      analysis.CreatedAt,
	}
}

// updateAnalysisStatus 更新分析状态
func (s *imageService) updateAnalysisStatus(ctx context.Context, id uint, status, errorMsg string) {
	analysis := &model.ImageAnalysis{
		ID:           id,
		Status:       status,
		ErrorMessage: errorMsg,
	}
	s.imageRepo.Update(ctx, analysis)
}

// structToJSON 结构体转JSON字符串
func (s *imageService) structToJSON(data interface{}) string {
	result, _ := json.Marshal(data)
	return string(result)
}
