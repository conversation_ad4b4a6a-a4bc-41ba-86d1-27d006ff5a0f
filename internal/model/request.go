package model

import "time"

// ImageAnalysisRequest 图片分析请求结构
type ImageAnalysisRequest struct {
	ImageURL string `json:"image_url" binding:"required" example:"https://example.com/image.jpg"`
}

// ProcessContext 贯穿整个处理流程的上下文结构体
type ProcessContext struct {
	TraceID     string    `json:"trace_id"`     // 追踪ID
	ImageURL    string    `json:"image_url"`    // 图片URL
	StartTime   time.Time `json:"start_time"`   // 开始处理时间
	Status      string    `json:"status"`       // 当前状态
	CurrentStep string    `json:"current_step"` // 当前步骤描述
}

// NewProcessContext 创建新的处理上下文
func NewProcessContext(traceID, imageURL string) *ProcessContext {
	return &ProcessContext{
		TraceID:     traceID,
		ImageURL:    imageURL,
		StartTime:   time.Now(),
		Status:      "received",
		CurrentStep: "请求已接收",
	}
}

// QwenRequest Qwen模型请求结构
type QwenRequest struct {
	Model    string                 `json:"model"`
	Messages []QwenMessage          `json:"messages"`
	Stream   bool                   `json:"stream"`
	Options  map[string]interface{} `json:"options,omitempty"`
}

// QwenMessage Qwen消息结构
type QwenMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"`
}

// QwenContent Qwen内容结构（支持文本和图片）
type QwenContent struct {
	Type     string `json:"type"`
	Text     string `json:"text,omitempty"`
	ImageURL string `json:"image_url,omitempty"`
}

// DeepSeekRequest DeepSeek模型请求结构
type DeepSeekRequest struct {
	Model       string          `json:"model"`
	Messages    []DeepSeekMessage `json:"messages"`
	Temperature float64         `json:"temperature,omitempty"`
	MaxTokens   int             `json:"max_tokens,omitempty"`
	Stream      bool            `json:"stream"`
}

// DeepSeekMessage DeepSeek消息结构
type DeepSeekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}
