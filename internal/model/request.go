package model

import "time"

// ImageAnalysisRequest 图片分析请求结构
type ImageAnalysisRequest struct {
	ImageURL string `json:"image_url" binding:"required" example:"https://example.com/image.jpg"`
}

// ProcessContext 贯穿整个处理流程的上下文结构体
type ProcessContext struct {
	// 基础信息
	TraceID   string    `json:"trace_id"`   // 追踪ID
	ImageURL  string    `json:"image_url"`  // 图片URL
	StartTime time.Time `json:"start_time"` // 开始处理时间

	// 处理状态
	Status      string `json:"status"`       // 当前状态: received, qwen_processing, qwen_completed, deepseek_processing, deepseek_completed, completed, failed
	CurrentStep string `json:"current_step"` // 当前步骤描述

	// 处理结果
	QwenRawResult    string                 `json:"qwen_raw_result,omitempty"`    // Qwen原始结果
	QwenParsedData   *ParsedQwenData       `json:"qwen_parsed_data,omitempty"`   // Qwen解析后的数据
	CacheKey         string                 `json:"cache_key,omitempty"`          // 缓存键
	DeepSeekResult   string                 `json:"deepseek_result,omitempty"`    // DeepSeek结果
	MergedResult     string                 `json:"merged_result,omitempty"`      // 合并后的最终结果

	// 错误信息
	Error        error  `json:"-"`              // 错误对象（不序列化）
	ErrorMessage string `json:"error_message,omitempty"` // 错误消息

	// 性能指标
	ProcessDuration int64 `json:"process_duration,omitempty"` // 总处理时长（毫秒）
	QwenDuration    int64 `json:"qwen_duration,omitempty"`    // Qwen处理时长（毫秒）
	DeepSeekDuration int64 `json:"deepseek_duration,omitempty"` // DeepSeek处理时长（毫秒）
}

// NewProcessContext 创建新的处理上下文
func NewProcessContext(traceID, imageURL string) *ProcessContext {
	return &ProcessContext{
		TraceID:     traceID,
		ImageURL:    imageURL,
		StartTime:   time.Now(),
		Status:      "received",
		CurrentStep: "请求已接收",
	}
}

// UpdateStatus 更新处理状态
func (pc *ProcessContext) UpdateStatus(status, step string) {
	pc.Status = status
	pc.CurrentStep = step
}

// SetError 设置错误信息
func (pc *ProcessContext) SetError(err error, message string) {
	pc.Error = err
	pc.ErrorMessage = message
	pc.Status = "failed"
	pc.CurrentStep = "处理失败: " + message
}

// CalculateDuration 计算总处理时长
func (pc *ProcessContext) CalculateDuration() {
	pc.ProcessDuration = time.Since(pc.StartTime).Milliseconds()
}

// IsCompleted 检查是否处理完成
func (pc *ProcessContext) IsCompleted() bool {
	return pc.Status == "completed"
}

// IsFailed 检查是否处理失败
func (pc *ProcessContext) IsFailed() bool {
	return pc.Status == "failed"
}

// QwenRequest Qwen模型请求结构
type QwenRequest struct {
	Model    string                 `json:"model"`
	Messages []QwenMessage          `json:"messages"`
	Stream   bool                   `json:"stream"`
	Options  map[string]interface{} `json:"options,omitempty"`
}

// QwenMessage Qwen消息结构
type QwenMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"`
}

// QwenContent Qwen内容结构（支持文本和图片）
type QwenContent struct {
	Type     string `json:"type"`
	Text     string `json:"text,omitempty"`
	ImageURL string `json:"image_url,omitempty"`
}

// DeepSeekRequest DeepSeek模型请求结构
type DeepSeekRequest struct {
	Model       string          `json:"model"`
	Messages    []DeepSeekMessage `json:"messages"`
	Temperature float64         `json:"temperature,omitempty"`
	MaxTokens   int             `json:"max_tokens,omitempty"`
	Stream      bool            `json:"stream"`
}

// DeepSeekMessage DeepSeek消息结构
type DeepSeekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}
