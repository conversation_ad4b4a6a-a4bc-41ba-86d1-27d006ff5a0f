package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"zhengshi/internal/config"
	"zhengshi/internal/model"
	"zhengshi/internal/utils"
)

// QwenClient Qwen客户端接口
type QwenClient interface {
	AnalyzeImage(ctx context.Context, imageURL string) (string, error)
}

// qwenClient Qwen客户端实现
type qwenClient struct {
	config     config.QwenConfig
	httpClient *http.Client
	logger     utils.Logger
}

// NewQwenClient 创建Qwen客户端
func NewQwenClient(cfg config.QwenConfig, logger utils.Logger) QwenClient {
	return &qwenClient{
		config: cfg,
		httpClient: &http.Client{
			Timeout: time.Duration(cfg.Timeout) * time.Second,
		},
		logger: logger,
	}
}

// AnalyzeImage 分析图片
func (c *qwenClient) AnalyzeImage(ctx context.Context, imageURL string) (string, error) {
	// 构建请求数据
	request := model.QwenRequest{
		Model:  c.config.Model,
		Stream: false,
		Messages: []model.QwenMessage{
			{
				Role: "user",
				Content: []model.QwenContent{
					{
						Type: "text",
						Text: "请详细分析这张图片，包括图片中的物体、场景、颜色、情感等信息，并以JSON格式返回结果。",
					},
					{
						Type:     "image_url",
						ImageURL: imageURL,
					},
				},
			},
		},
	}

	// 序列化请求数据
	requestBody, err := json.Marshal(request)
	if err != nil {
		c.logger.Error("Failed to marshal Qwen request", err, "image_url", imageURL)
		return "", fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", c.config.BaseURL, bytes.NewBuffer(requestBody))
	if err != nil {
		c.logger.Error("Failed to create Qwen HTTP request", err, "image_url", imageURL)
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.config.APIKey)

	c.logger.Info("Sending request to Qwen API", 
		"url", c.config.BaseURL,
		"model", c.config.Model,
		"image_url", imageURL,
	)

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logger.Error("Qwen API request failed", err, "image_url", imageURL)
		return "", fmt.Errorf("Qwen API请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.logger.Error("Failed to read Qwen response", err, "image_url", imageURL)
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		c.logger.Error("Qwen API returned error status", nil,
			"status_code", resp.StatusCode,
			"response", string(responseBody),
			"image_url", imageURL,
		)
		return "", fmt.Errorf("Qwen API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var qwenResponse model.QwenResponse
	if err := json.Unmarshal(responseBody, &qwenResponse); err != nil {
		c.logger.Error("Failed to unmarshal Qwen response", err, 
			"response", string(responseBody),
			"image_url", imageURL,
		)
		return "", fmt.Errorf("解析Qwen响应失败: %w", err)
	}

	// 提取响应内容
	if len(qwenResponse.Choices) == 0 {
		c.logger.Error("Qwen response has no choices", nil, "image_url", imageURL)
		return "", fmt.Errorf("Qwen响应中没有选择项")
	}

	content := qwenResponse.Choices[0].Message.Content
	contentStr, ok := content.(string)
	if !ok {
		c.logger.Error("Qwen response content is not string", nil, "image_url", imageURL)
		return "", fmt.Errorf("Qwen响应内容格式错误")
	}

	c.logger.Info("Qwen API request completed successfully",
		"image_url", imageURL,
		"tokens_used", qwenResponse.Usage.TotalTokens,
	)

	return contentStr, nil
}
