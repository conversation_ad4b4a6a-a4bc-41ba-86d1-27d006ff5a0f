使用golang开发一个web的api服务；
框架要求gin；
mysql8；
redis；

用户携带一个图片url请求api；

api将图片url提交给qwen模型；

得到qwen响应数据后，对其进行格式化解析；

将解析后的数据制作缓存键名称；

将解析后的数据请求给deepseek模型；

将deepseek模型响应数据与解析后的部分qwen数据进行合并存入数据库并返回给用户；


业务要求；
1. 高标准、高规范化开发；
2. 尽可能的将业务模块封装成独立的方法，以便于后续业务功能扩展；
3. 规范化的目录结构；
4. 规范命名；
5. 规范的错误码；
6. 完善的调试日志；


---------------------------------------------------

### mysql与redis的配置信息



MySQL_8数据库
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=Suyan15913..
MYSQL_DATABASE=t_solve_go_api
MYSQL_CHARSET=utf8mb4

REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=Suyan15913..
REDIS_DB=0

### qen与deepseek的key信息

QWEN_KEY=***********************************	
DEEPSEEK_KEY=***********************************
