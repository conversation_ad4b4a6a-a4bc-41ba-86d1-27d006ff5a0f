#!/bin/bash

# 简单快速重启脚本
echo "🔄 快速重启服务..."

# 强制杀死所有相关进程
echo "🛑 停止服务..."
pkill -f "zhengshi" 2>/dev/null
pkill -f "go run" 2>/dev/null
lsof -ti:8080 | xargs kill -9 2>/dev/null
sleep 2

# 启动服务
echo "🚀 启动服务..."
nohup go run ./cmd/server/main.go > ./logs/app.log 2>&1 &
echo $! > ./logs/app.pid

# 等待启动
echo "⏳ 等待启动..."
sleep 3

# 检查状态
if curl -s http://localhost:8080/health >/dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "🔗 健康检查: http://localhost:8080/health"
else
    echo "❌ 服务启动失败，查看日志: tail -f ./logs/app.log"
fi
